# fllow

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```

# ACTUAL

This is project for research and proof  of concept.

# IDEA

lets say we have e-shop app.
with users, user subscrriptions tiers, e-products.
we have complex purchase flow:

we use xstate for it and im not sure that our aproach is correct and optimal.


due to business logic flow is very different. it became complicated structure which is very hard to maintain and update.

so we use xstate
context to keep state, steps, evens (with back and such).
but managers keep complicating flows so it became cumbersome.

here is various variables on which flow may depend:
user is having any subscription
user is having subscribtion tier above specific.
user KYC level
product category
user balance (link to topup page)



its basically multistep purchase flow that creates object that sent to backend at the end
e.g. success step preset in all conditions.

lets try to simulate this first.

lets create simple page with xstate. lets imagine some flows for it.
imagine please some shop with similiar functioalitiy.

lets start with simple stuff

---

So we installed tailwind and xstate.

---
Lets create vue page, where we will recreate our purchase flow.|

---

Ok we recreated some flow

--- 

now lets rewrite to use @xstate/vue

https://stately.ai/docs/xstate-vue