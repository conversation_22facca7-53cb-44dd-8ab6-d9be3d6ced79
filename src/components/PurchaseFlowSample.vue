<script setup lang="ts">
import { createMachine } from 'xstate'
import { useMachine } from '@xstate/vue'

// Define the purchase flow state machine
const purchaseFlowMachine = createMachine({
  id: 'purchaseFlow',
  initial: 'productSelection',
  context: {
    // Store any data related to the purchase flow
    product: null,
    userInfo: {
      subscription: 'Premium',
      kycLevel: 'Level 2',
      balance: 500.00
    }
  },
  states: {
    productSelection: {
      on: {
        NEXT: 'userInformation'
      }
    },
    userInformation: {
      on: {
        NEXT: 'confirmation',
        BACK: 'productSelection'
      }
    },
    confirmation: {
      on: {
        BACK: 'userInformation',
        SUBMIT: 'success'
      }
    },
    success: {
      type: 'final'
    }
  }
})

// Use the state machine in the component
const { snapshot: state, send } = useMachine(purchaseFlowMachine)

// Helper functions to navigate between steps
const nextStep = () => {
  send({ type: 'NEXT' })
}

const prevStep = () => {
  send({ type: 'BACK' })
}

// Helper function to get current step number for progress bar
const getCurrentStep = () => {
  switch (state.value.value.) {
    case 'productSelection':
      return 1
    case 'userInformation':
      return 2
    case 'confirmation':
      return 3
    default:
      return 0
  }
}

// Total number of steps
const totalSteps = 3
</script>

<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <div class="mb-4">
      <div class="flex items-center justify-between mb-2">
        <h2 class="text-xl font-semibold">Sample Purchase Flow</h2>
        <div class="text-sm text-gray-500">Step {{ getCurrentStep() }} of {{ totalSteps }}</div>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2.5">
        <div
          class="bg-blue-600 h-2.5 rounded-full"
          :style="{ width: `${(getCurrentStep() / totalSteps) * 100}%` }"
        ></div>
      </div>
    </div>

    <div class="min-h-[200px] mb-4">
      <!-- Product Selection Step -->
      <div v-if="state.matches('productSelection')" class="space-y-4">
        <h3 class="text-lg font-medium">Product Selection</h3>
        <p class="text-gray-600">
          This is where the user would select products to purchase.
          This is now connected to XState for managing the flow.
        </p>
        <div class="p-4 border rounded-md bg-gray-50">
          <p class="font-medium">Sample Product</p>
          <p class="text-gray-500">$99.99</p>
        </div>
      </div>

      <!-- User Information Step -->
      <div v-if="state.matches('userInformation')" class="space-y-4">
        <h3 class="text-lg font-medium">User Information</h3>
        <p class="text-gray-600">
          This is where the user would enter their information.
          The flow might change based on user subscription tier, KYC level, etc.
        </p>
        <div class="space-y-2">
          <div class="flex items-center">
            <span class="w-32 text-gray-600">Subscription:</span>
            <span class="font-medium">{{ state.context.userInfo.subscription }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-32 text-gray-600">KYC Level:</span>
            <span class="font-medium">{{ state.context.userInfo.kycLevel }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-32 text-gray-600">Balance:</span>
            <span class="font-medium">${{ state.context.userInfo.balance.toFixed(2) }}</span>
          </div>
        </div>
      </div>

      <!-- Confirmation Step -->
      <div v-if="state.matches('confirmation')" class="space-y-4">
        <h3 class="text-lg font-medium">Confirmation</h3>
        <p class="text-gray-600">
          This is the final step where the user would confirm their purchase.
          The purchase data would be sent to the backend.
        </p>
        <div class="p-4 border rounded-md bg-green-50 text-green-700">
          Your purchase is ready to be confirmed!
        </div>
      </div>

      <!-- Success Step -->
      <div v-if="state.matches('success')" class="space-y-4">
        <h3 class="text-lg font-medium">Success!</h3>
        <p class="text-gray-600">
          Your purchase has been successfully completed.
        </p>
        <div class="p-4 border rounded-md bg-green-50 text-green-700">
          Thank you for your purchase!
        </div>
      </div>
    </div>

    <div class="flex justify-between">
      <button
        @click="prevStep"
        class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md disabled:opacity-50"
        :disabled="state.matches('productSelection') || state.matches('success')"
      >
        Back
      </button>
      <button
        v-if="!state.matches('confirmation') && !state.matches('success')"
        @click="nextStep"
        class="px-4 py-2 bg-blue-600 text-white rounded-md"
      >
        Next
      </button>
      <button
        v-if="state.matches('confirmation')"
        @click="send('SUBMIT')"
        class="px-4 py-2 bg-green-600 text-white rounded-md"
      >
        Confirm Purchase
      </button>
    </div>
  </div>
</template>
