# Project Guidelines for Fllow

## Project Overview
Fllow is a research and proof of concept project for an e-shop application with complex purchase flows. The project uses Vue 3 with Vite as the frontend framework and XState for state management of complex purchase flows.

## Project Structure
- `/src` - Main source code directory
  - `/assets` - Static assets like CSS and images
  - `/components` - Reusable Vue components
  - `/router` - Vue Router configuration
  - `/stores` - State management (Pinia stores)
  - `/views` - Page components that correspond to routes
- `/public` - Public static files
- `/.junie` - Guidelines for Junie AI assistant

## Development Workflow
1. Install dependencies: `npm install`
2. Start development server: `npm run dev`
3. Build for production: `npm run build`
4. Lint code: `npm run lint`

## Testing
When implementing new features or fixing bugs, <PERSON><PERSON> should:
- Verify that the implementation meets the requirements
- Ensure that the code is compatible with Vue 3 and TypeScript
- Test the changes in the development environment if possible

## Build Process
Before submitting changes, <PERSON><PERSON> should ensure that the project builds successfully:
```
npm run build
```

## Code Style Guidelines
- Use TypeScript for type safety
- Follow Vue 3 Composition API patterns
- Use `<script setup>` syntax for Vue components
- Maintain consistent indentation (2 spaces)
- Use meaningful component and variable names
- Add comments for complex logic
- Organize imports alphabetically
- Use Tailwind CSS for styling

## XState Guidelines
- Keep state machines focused on a single responsibility
- Document state transitions clearly
- Use context to store state data
- Implement guards for conditional transitions
- Use actions for side effects

## Purchase Flow Implementation
The purchase flow is the core feature of this application. It depends on various factors:
- User subscription status
- User subscription tier
- User KYC level
- Product category
- User balance

The flow should be implemented using XState to manage the complex state transitions and conditions.
